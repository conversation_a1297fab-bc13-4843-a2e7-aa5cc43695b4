/**
 * Autonomous Agent Loop & Tool Calling System
 * 
 * The heart of the AI system implementing sophisticated autonomous workflow
 * <PERSON>les tool calling, conversation management, and state persistence
 */

import type { OpenAI } from 'openai';
import { createOpenAIClient } from '../openai-client.js';
import { loadConfig } from '../config.js';
import { logAgentExecution, logError } from '../logger/log.js';
import type {
  AppConfig,
  ApprovalPolicy,
  ResponseItem,
  ResponseInputItem,
  ResponseOutputItem,
  ResponseFunctionToolCall,
  ResponseToolResult,
  ExecInput,
  FunctionTool
} from '../../types/index.js';
import { handleExecCommand } from './handle-exec-command.js';
import { convertMessagesToOpenAI, estimateTokenCount, debugMessageConversion } from '../responses.js';

export interface AgentLoopConfig {
  model: string;
  provider: string;
  approvalPolicy: ApprovalPolicy;
  maxIterations?: number;
  timeout?: number;
  additionalWritableRoots?: string[];
  singlePass?: boolean;
  planningMode?: boolean;
  executionMode?: boolean;
  validationMode?: boolean;
  useFullContext?: boolean;
}

export interface AgentLoopCallbacks {
  onDelta?: (delta: string) => void;
  onComplete?: (content: string) => void;
  onError?: (error: string) => void;
  onToolCall?: (toolCall: ResponseFunctionToolCall) => void;
  onToolResult?: (result: ResponseToolResult) => void;
  getCommandConfirmation?: (command: string[], workdir: string) => Promise<boolean>;
}

/**
 * Core Agent Loop Class
 */
export class AgentLoop {
  private model: string;
  private provider: string;
  private oai: OpenAI;
  private approvalPolicy: ApprovalPolicy;
  private transcript: ResponseInputItem[] = [];
  private cumulativeThinkingMs = 0;
  private additionalWritableRoots: string[];
  private config: AppConfig;

  constructor(config: AgentLoopConfig) {
    this.model = config.model;
    this.provider = config.provider;
    this.approvalPolicy = config.approvalPolicy;
    this.additionalWritableRoots = config.additionalWritableRoots || [];
    this.config = loadConfig();
    
    // Create OpenAI client
    this.oai = createOpenAIClient({
      provider: this.provider,
      timeout: config.timeout
    });
  }

  /**
   * Execute agent loop with user input
   */
  async executeLoop(
    userInput: ResponseInputItem,
    callbacks?: AgentLoopCallbacks,
    maxIterations?: number
  ): Promise<ResponseItem[]>;

  async executeLoop(
    userInput: ResponseInputItem,
    options: {
      callbacks?: AgentLoopCallbacks;
      maxIterations?: number;
      singlePass?: boolean;
      planningMode?: boolean;
      executionMode?: boolean;
      validationMode?: boolean;
    }
  ): Promise<ResponseItem[]>;

  async executeLoop(
    userInput: ResponseInputItem,
    callbacksOrOptions: AgentLoopCallbacks | {
      callbacks?: AgentLoopCallbacks;
      maxIterations?: number;
      singlePass?: boolean;
      planningMode?: boolean;
      executionMode?: boolean;
      validationMode?: boolean;
    } = {},
    maxIterations: number = 10
  ): Promise<ResponseItem[]> {
    // Handle overloaded parameters
    let callbacks: AgentLoopCallbacks = {};
    let actualMaxIterations = maxIterations;
    let singlePass = false;

    if ('callbacks' in callbacksOrOptions || 'maxIterations' in callbacksOrOptions) {
      // New options format
      const options = callbacksOrOptions as any;
      callbacks = options.callbacks || {};
      actualMaxIterations = options.maxIterations || 10;
      singlePass = options.singlePass || false;
      // Note: planningMode, executionMode, validationMode are reserved for future use
    } else {
      // Legacy callbacks format
      callbacks = callbacksOrOptions as AgentLoopCallbacks;
    }

    // Single pass mode limits iterations to 1
    if (singlePass) {
      actualMaxIterations = 1;
    }
    const startTime = Date.now();
    const results: ResponseItem[] = [];
    
    try {
      // Add user input to transcript
      this.transcript.push(userInput);
      results.push(userInput);

      logAgentExecution('loop_start', {
        model: this.model,
        provider: this.provider,
        approvalPolicy: this.approvalPolicy
      });

      let iteration = 0;
      while (iteration < actualMaxIterations) {
        iteration++;
        
        logAgentExecution('iteration_start', { iteration });

        // Convert transcript to OpenAI format
        const messages = convertMessagesToOpenAI([...this.transcript]);

        // Debug message conversion if in debug mode
        if (process.env.DEBUG) {
          const debugInfo = debugMessageConversion([...this.transcript]);
          logAgentExecution('message_conversion_debug', debugInfo);
        }

        // Validate messages array
        if (messages.length === 0) {
          const debugInfo = debugMessageConversion([...this.transcript]);
          const errorDetails = `Transcript items: ${debugInfo.inputItems} inputs, ${debugInfo.outputItems} outputs. Details: ${JSON.stringify(debugInfo.details)}`;
          throw new Error(`No valid messages to send to AI provider. Check that your input contains text content. ${errorDetails}`);
        }

        // Check token limits
        const tokenCount = estimateTokenCount(messages);
        logAgentExecution('token_count', { tokens: tokenCount, messageCount: messages.length });

        // Prepare tools
        const tools = this.getAvailableTools();

        // Make AI request
        const iterationStartTime = Date.now();
        
        try {
          const completion = await this.oai.chat.completions.create({
            model: this.model,
            messages,
            tools: tools.length > 0 ? tools : undefined,
            tool_choice: tools.length > 0 ? 'auto' : undefined,
            temperature: this.config.temperature || 0.7,
            max_tokens: this.config.maxTokens || 4096,
            stream: false
          } as any);

          const iterationTime = Date.now() - iterationStartTime;
          this.cumulativeThinkingMs += iterationTime;

          const choice = completion.choices?.[0];
          if (!choice) {
            throw new Error('No response choice received');
          }

          // Handle content
          const content = choice.message?.content || '';
          if (content.trim()) {
            callbacks.onDelta?.(content);
            callbacks.onComplete?.(content);

            const assistantResponse: ResponseOutputItem = {
              role: 'assistant',
              content,
              type: 'output',
              timestamp: Date.now(),
              metadata: {
                model: this.model,
                provider: this.provider,
                thinkingTime: iterationTime
              }
            };

            results.push(assistantResponse);
          }

          // Handle function calls
          if (choice.message?.tool_calls) {
            for (const toolCall of choice.message.tool_calls) {
              if (toolCall.type === 'function') {
                const functionCall: ResponseFunctionToolCall = {
                  id: toolCall.id,
                  name: toolCall.function.name,
                  arguments: toolCall.function.arguments,
                  type: 'function_call',
                  timestamp: Date.now()
                };

                results.push(functionCall);
                callbacks.onToolCall?.(functionCall);

                // Execute function call
                const toolResults = await this.handleFunctionCall(functionCall, callbacks);
                results.push(...toolResults);
              }
            }
          } else {
            // No function calls, conversation is complete
            logAgentExecution('loop_complete', {
              iterations: iteration,
              totalTime: Date.now() - startTime
            });
            return results;
          }

        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          logError('Agent loop iteration failed', error instanceof Error ? error : new Error(errorMessage));
          callbacks.onError?.(errorMessage);
          throw error;
        }
      }

      logAgentExecution('loop_max_iterations', { maxIterations: actualMaxIterations });
      return results;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      logError('Agent loop failed', error instanceof Error ? error : new Error(errorMessage));
      callbacks.onError?.(errorMessage);
      throw error;
    }
  }

  /**
   * Handle function call execution
   */
  private async handleFunctionCall(
    functionCall: ResponseFunctionToolCall,
    callbacks: AgentLoopCallbacks
  ): Promise<ResponseToolResult[]> {
    const { name, arguments: argsString } = functionCall;
    
    try {
      // Parse arguments
      let args: any;
      try {
        args = JSON.parse(argsString);
      } catch (error) {
        throw new Error(`Invalid function arguments: ${argsString}`);
      }

      if (name === 'shell' || name === 'local_shell') {
        return await this.handleShellCommand(args, functionCall.id, callbacks);
      }

      throw new Error(`Unknown function: ${name}`);

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      const errorResult: ResponseToolResult = {
        id: functionCall.id,
        result: `Error: ${errorMessage}`,
        success: false,
        type: 'tool_result',
        timestamp: Date.now()
      };

      return [errorResult];
    }
  }

  /**
   * Handle shell command execution
   */
  private async handleShellCommand(
    args: any,
    toolCallId: string,
    callbacks: AgentLoopCallbacks
  ): Promise<ResponseToolResult[]> {
    try {
      const execInput: ExecInput = {
        command: args.command || [],
        workdir: args.workdir || process.cwd(),
        timeout: args.timeout || 30000
      };

      // Get command confirmation if needed
      const needsApproval = this.approvalPolicy === 'suggest' || 
                           (this.approvalPolicy === 'auto-edit' && !this.isCommandSafe(execInput.command));

      if (needsApproval && callbacks.getCommandConfirmation) {
        const approved = await callbacks.getCommandConfirmation(execInput.command, execInput.workdir || process.cwd());
        if (!approved) {
          const deniedResult: ResponseToolResult = {
            id: toolCallId,
            result: 'Command execution denied by user',
            success: false,
            type: 'tool_result',
            timestamp: Date.now(),
            metadata: {
              command: execInput.command,
              workdir: execInput.workdir
            }
          };
          return [deniedResult];
        }
      }

      // Execute command
      const execResult = await handleExecCommand(
        execInput,
        this.config,
        this.approvalPolicy,
        this.additionalWritableRoots
      );

      const toolResult: ResponseToolResult = {
        id: toolCallId,
        result: execResult.output,
        success: execResult.success,
        type: 'tool_result',
        timestamp: Date.now(),
        metadata: {
          command: execResult.command,
          workdir: execResult.workdir,
          exitCode: execResult.exitCode,
          duration: execResult.duration
        }
      };

      callbacks.onToolResult?.(toolResult);
      return [toolResult];

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      const errorResult: ResponseToolResult = {
        id: toolCallId,
        result: `Error executing command: ${errorMessage}`,
        success: false,
        type: 'tool_result',
        timestamp: Date.now()
      };

      return [errorResult];
    }
  }

  /**
   * Check if command is safe for auto-approval
   */
  private isCommandSafe(command: string[]): boolean {
    const safeCommands = this.config.safeCommands || [];
    const commandName = command[0]?.toLowerCase();
    return safeCommands.includes(commandName);
  }

  /**
   * Get available tools based on approval policy
   */
  private getAvailableTools(): FunctionTool[] {
    const tools: FunctionTool[] = [];

    // Shell tool
    tools.push({
      type: 'function',
      function: {
        name: 'shell',
        description: 'Execute shell commands and return their output. Use this to run system commands, file operations, and interact with the environment.',
        parameters: {
          type: 'object',
          properties: {
            command: {
              type: 'array',
              items: { type: 'string' },
              description: 'The command to execute as an array of strings (command and arguments)'
            },
            workdir: {
              type: 'string',
              description: 'Working directory for command execution (optional, defaults to current directory)'
            },
            timeout: {
              type: 'number',
              description: 'Timeout in milliseconds (optional, defaults to 30000)'
            }
          },
          required: ['command']
        }
      }
    });

    return tools;
  }

  /**
   * Get conversation transcript
   */
  getTranscript(): ResponseInputItem[] {
    return [...this.transcript];
  }

  /**
   * Clear conversation transcript
   */
  clearTranscript(): void {
    this.transcript = [];
  }

  /**
   * Get cumulative thinking time
   */
  getCumulativeThinkingTime(): number {
    return this.cumulativeThinkingMs;
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<AgentLoopConfig>): void {
    if (newConfig.model) this.model = newConfig.model;
    if (newConfig.provider) this.provider = newConfig.provider;
    if (newConfig.approvalPolicy) this.approvalPolicy = newConfig.approvalPolicy;
    if (newConfig.additionalWritableRoots) this.additionalWritableRoots = newConfig.additionalWritableRoots;

    // Recreate OpenAI client if provider changed
    if (newConfig.provider || newConfig.model) {
      this.oai = createOpenAIClient({
        provider: this.provider,
        timeout: newConfig.timeout
      });
    }
  }
}
